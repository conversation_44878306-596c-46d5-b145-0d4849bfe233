#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复扫描任务页面的详情按钮选择器
"""

import json
import os

def fix_scan_task_details():
    """修复扫描任务页面的详情按钮"""
    
    rules_file = "ar_ui_rules/ar_ui_selectors.json"
    
    # 读取现有规则
    with open(rules_file, 'r', encoding='utf-8') as f:
        rules = json.load(f)
    
    # 修复扫描任务页面
    if '扫描任务' in rules:
        if 'special_elements' not in rules['扫描任务']['selectors']:
            rules['扫描任务']['selectors']['special_elements'] = {}
        
        # 添加详情按钮选择器
        rules['扫描任务']['selectors']['special_elements']['details_buttons'] = {
            'xpath': "//button[contains(text(), '详情')] | //a[contains(text(), '详情')]",
            'description': "详情按钮",
            'usage': "用于查看扫描任务详情"
        }
        
        # 添加其他常用的扫描任务操作按钮
        rules['扫描任务']['selectors']['special_elements']['action_buttons'] = {
            'xpath': "//td[contains(@class, 'action') or contains(., '操作')]//button",
            'description': "操作列按钮",
            'usage': "表格中的操作按钮"
        }
        
        print("✅ 已修复扫描任务页面的详情按钮选择器")
    
    # 同时为其他可能需要详情按钮的页面添加通用选择器
    pages_need_details = ['升级任务', '计划任务', '用户管理']
    
    for page_name in pages_need_details:
        if page_name in rules:
            if 'special_elements' not in rules[page_name]['selectors']:
                rules[page_name]['selectors']['special_elements'] = {}
            
            if 'details_buttons' not in rules[page_name]['selectors']['special_elements']:
                rules[page_name]['selectors']['special_elements']['details_buttons'] = {
                    'xpath': "//button[contains(text(), '详情')] | //a[contains(text(), '详情')]",
                    'description': "详情按钮",
                    'usage': "用于查看详细信息"
                }
                print(f"✅ 已为 {page_name} 页面添加详情按钮选择器")
    
    # 保存更新后的规则
    with open(rules_file, 'w', encoding='utf-8') as f:
        json.dump(rules, f, ensure_ascii=False, indent=2)
    
    print("🎉 规则库修复完成！")

if __name__ == "__main__":
    fix_scan_task_details()
