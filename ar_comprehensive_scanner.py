#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心全面扫描器
扫描所有可能的页面和子页面，包括对话框和弹窗
"""

import time
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class ARComprehensiveScanner:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.scan_results = {}
        self.scanned_urls = set()
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
        
        # 输出目录
        self.output_dir = "ar_ui_rules"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def discover_all_clickable_elements(self):
        """发现所有可点击的元素"""
        try:
            print("\n🔍 发现所有可点击元素...")
            
            clickable_elements = []
            
            # 查找所有可能的可点击元素
            selectors = [
                "//span[contains(@class, 'el-menu-item') or contains(@class, 'el-submenu__title')]",
                "//a[contains(@href, '#')]",
                "//li[contains(@class, 'el-menu-item')]",
                "//div[contains(@class, 'el-menu-item')]",
                "//button[not(contains(@class, 'disabled'))]",
                "//*[contains(@class, 'clickable')]",
                "//*[@onclick]"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            text = elem.text.strip()
                            if text and len(text) < 50 and text not in [e['text'] for e in clickable_elements]:
                                clickable_elements.append({
                                    'text': text,
                                    'element': elem,
                                    'tag': elem.tag_name,
                                    'class': elem.get_attribute('class')
                                })
                except:
                    continue
            
            print(f"   发现 {len(clickable_elements)} 个可点击元素")
            for elem in clickable_elements[:10]:  # 只显示前10个
                print(f"      - {elem['text']} ({elem['tag']})")
            
            return clickable_elements
            
        except Exception as e:
            print(f"❌ 发现可点击元素失败: {e}")
            return []
    
    def analyze_current_page(self, page_name=None):
        """分析当前页面"""
        try:
            current_url = self.driver.current_url
            if not page_name:
                page_name = f"页面_{len(self.scan_results) + 1}"
            
            print(f"🔍 分析页面: {page_name}")
            
            page_data = {
                'page_name': page_name,
                'page_url': current_url,
                'scan_time': datetime.now().isoformat(),
                'elements': {}
            }
            
            # 分析各种元素
            page_data['elements']['buttons'] = self._analyze_buttons()
            page_data['elements']['inputs'] = self._analyze_inputs()
            page_data['elements']['tables'] = self._analyze_tables()
            page_data['elements']['checkboxes'] = self._analyze_checkboxes()
            page_data['elements']['radios'] = self._analyze_radios()
            page_data['elements']['selects'] = self._analyze_selects()
            page_data['elements']['dialogs'] = self._analyze_dialogs()
            page_data['elements']['forms'] = self._analyze_forms()
            
            # 统计信息
            total_elements = sum(len(elements) for elements in page_data['elements'].values())
            print(f"   总共找到 {total_elements} 个UI元素")
            
            return page_data
            
        except Exception as e:
            print(f"❌ 分析页面失败: {e}")
            return None
    
    def _analyze_buttons(self):
        """分析按钮"""
        buttons = []
        try:
            elements = self.driver.find_elements(By.XPATH, "//button")
            for elem in elements:
                if elem.is_displayed():
                    button_info = {
                        'text': elem.text.strip(),
                        'class': elem.get_attribute('class'),
                        'type': elem.get_attribute('type'),
                        'disabled': elem.get_attribute('disabled') is not None,
                        'xpath_by_text': f"//button[contains(text(), '{elem.text.strip()}')]" if elem.text.strip() else None,
                        'xpath_by_class': f"//button[contains(@class, '{elem.get_attribute('class').split()[0]}')]" if elem.get_attribute('class') else None
                    }
                    buttons.append(button_info)
        except:
            pass
        return buttons
    
    def _analyze_inputs(self):
        """分析输入框"""
        inputs = []
        try:
            elements = self.driver.find_elements(By.XPATH, "//input | //textarea")
            for elem in elements:
                if elem.is_displayed():
                    input_info = {
                        'type': elem.get_attribute('type'),
                        'name': elem.get_attribute('name'),
                        'id': elem.get_attribute('id'),
                        'placeholder': elem.get_attribute('placeholder'),
                        'class': elem.get_attribute('class'),
                        'required': elem.get_attribute('required') is not None,
                        'xpath_by_name': f"//input[@name='{elem.get_attribute('name')}']" if elem.get_attribute('name') else None,
                        'xpath_by_id': f"//input[@id='{elem.get_attribute('id')}']" if elem.get_attribute('id') else None,
                        'xpath_by_placeholder': f"//input[@placeholder='{elem.get_attribute('placeholder')}']" if elem.get_attribute('placeholder') else None
                    }
                    inputs.append(input_info)
        except:
            pass
        return inputs
    
    def _analyze_tables(self):
        """分析表格"""
        tables = []
        try:
            elements = self.driver.find_elements(By.XPATH, "//table")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    # 获取表头
                    headers = []
                    try:
                        header_elements = elem.find_elements(By.XPATH, ".//th")
                        headers = [th.text.strip() for th in header_elements if th.text.strip()]
                    except:
                        pass
                    
                    table_info = {
                        'index': i + 1,
                        'class': elem.get_attribute('class'),
                        'headers': headers,
                        'xpath': f"(//table)[{i + 1}]",
                        'xpath_by_class': f"//table[contains(@class, '{elem.get_attribute('class').split()[0]}')]" if elem.get_attribute('class') else None
                    }
                    tables.append(table_info)
        except:
            pass
        return tables
    
    def _analyze_checkboxes(self):
        """分析复选框"""
        checkboxes = []
        try:
            # Element UI 复选框
            elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'el-checkbox__inner')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    checkbox_info = {
                        'type': 'element-ui',
                        'index': i + 1,
                        'xpath': f"(//span[contains(@class, 'el-checkbox__inner')])[{i + 1}]",
                        'general_xpath': "//span[contains(@class, 'el-checkbox__inner')]"
                    }
                    checkboxes.append(checkbox_info)
            
            # 原生复选框
            elements = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    checkbox_info = {
                        'type': 'native',
                        'index': i + 1,
                        'name': elem.get_attribute('name'),
                        'xpath': f"(//input[@type='checkbox'])[{i + 1}]",
                        'xpath_by_name': f"//input[@type='checkbox' and @name='{elem.get_attribute('name')}']" if elem.get_attribute('name') else None
                    }
                    checkboxes.append(checkbox_info)
        except:
            pass
        return checkboxes
    
    def _analyze_radios(self):
        """分析单选框"""
        radios = []
        try:
            # Element UI 单选框
            elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'el-radio__inner')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    # 获取标签文本
                    label_text = ""
                    try:
                        label_elem = elem.find_element(By.XPATH, "./ancestor::label[1]//span[contains(@class, 'el-radio__label')]")
                        label_text = label_elem.text.strip()
                    except:
                        pass
                    
                    radio_info = {
                        'type': 'element-ui',
                        'index': i + 1,
                        'label': label_text,
                        'xpath': f"(//span[contains(@class, 'el-radio__inner')])[{i + 1}]",
                        'xpath_by_label': f"//span[contains(@class, 'el-radio__label') and contains(text(), '{label_text}')]" if label_text else None
                    }
                    radios.append(radio_info)
        except:
            pass
        return radios
    
    def _analyze_selects(self):
        """分析下拉框"""
        selects = []
        try:
            # Element UI 下拉框
            elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-select')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    select_info = {
                        'type': 'element-ui',
                        'index': i + 1,
                        'class': elem.get_attribute('class'),
                        'xpath': f"(//div[contains(@class, 'el-select')])[{i + 1}]"
                    }
                    selects.append(select_info)
            
            # 原生下拉框
            elements = self.driver.find_elements(By.XPATH, "//select")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    select_info = {
                        'type': 'native',
                        'index': i + 1,
                        'name': elem.get_attribute('name'),
                        'xpath': f"(//select)[{i + 1}]"
                    }
                    selects.append(select_info)
        except:
            pass
        return selects
    
    def _analyze_dialogs(self):
        """分析对话框"""
        dialogs = []
        try:
            elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'el-dialog')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    dialog_info = {
                        'index': i + 1,
                        'class': elem.get_attribute('class'),
                        'xpath': f"(//div[contains(@class, 'el-dialog')])[{i + 1}]",
                        'visible': elem.is_displayed()
                    }
                    dialogs.append(dialog_info)
        except:
            pass
        return dialogs
    
    def _analyze_forms(self):
        """分析表单"""
        forms = []
        try:
            elements = self.driver.find_elements(By.XPATH, "//form | //div[contains(@class, 'el-form')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    form_info = {
                        'index': i + 1,
                        'tag': elem.tag_name,
                        'class': elem.get_attribute('class'),
                        'xpath': f"({elem.tag_name})[{i + 1}]"
                    }
                    forms.append(form_info)
        except:
            pass
        return forms
    
    def comprehensive_scan(self):
        """全面扫描"""
        try:
            print("\n🚀 开始全面扫描AR控制中心...")
            
            # 扫描主页面
            main_page = self.analyze_current_page("主页面")
            if main_page:
                self.scan_results["main_page"] = main_page
                self.scanned_urls.add(self.driver.current_url)
            
            # 发现所有可点击元素
            clickable_elements = self.discover_all_clickable_elements()
            
            # 逐个点击并扫描
            for i, elem_info in enumerate(clickable_elements):
                try:
                    print(f"\n{'='*15} 点击元素: {elem_info['text']} ({i+1}/{len(clickable_elements)}) {'='*15}")
                    
                    # 点击元素
                    self.driver.execute_script("arguments[0].click();", elem_info['element'])
                    time.sleep(2)
                    
                    # 检查URL是否变化
                    current_url = self.driver.current_url
                    if current_url not in self.scanned_urls:
                        page_data = self.analyze_current_page(elem_info['text'])
                        if page_data:
                            safe_key = elem_info['text'].replace(' ', '_').replace('/', '_').replace(':', '_')
                            self.scan_results[safe_key] = page_data
                            self.scanned_urls.add(current_url)
                    else:
                        print(f"   页面已扫描过，跳过")
                    
                except Exception as e:
                    print(f"   点击元素失败: {e}")
                    continue
            
            print(f"\n✅ 全面扫描完成！共扫描了 {len(self.scan_results)} 个页面")
            return True
            
        except Exception as e:
            print(f"❌ 全面扫描失败: {e}")
            return False
    
    def save_comprehensive_rules(self):
        """保存全面规则库"""
        try:
            print("\n📝 保存全面规则库...")
            
            # 保存详细的JSON规则
            json_file = os.path.join(self.output_dir, "ar_comprehensive_rules.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(self.scan_results, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 全面规则库保存完成: {json_file}")
            print(f"   包含 {len(self.scan_results)} 个页面的详细UI元素信息")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存规则库失败: {e}")
            return False
    
    def run_comprehensive_scan(self):
        """运行全面扫描"""
        print("🔍 AR控制中心全面扫描器")
        print("=" * 60)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login():
                return False
            
            if not self.comprehensive_scan():
                return False
            
            if not self.save_comprehensive_rules():
                return False
            
            print("\n🎉 全面扫描完成！")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    scanner = ARComprehensiveScanner()
    success = scanner.run_comprehensive_scan()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
