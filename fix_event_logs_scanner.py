#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复事件日志扫描器
正确扫描事件日志页面和其子页面
"""

import time
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class EventLogsScanner:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.scan_results = {}
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
        
        # 输出目录
        self.output_dir = "ar_ui_rules"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def navigate_to_event_logs(self):
        """导航到事件日志页面"""
        try:
            print("🔄 导航到事件日志...")
            
            # 点击事件日志主菜单
            event_log_selectors = [
                "//span[contains(text(), '事件日志')]",
                "//a[contains(text(), '事件日志')]",
                "//*[text()='事件日志']"
            ]
            
            for selector in event_log_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed():
                            print(f"   点击事件日志菜单")
                            self.driver.execute_script("arguments[0].click();", elem)
                            time.sleep(3)
                            
                            # 检查是否展开了子菜单
                            print("   检查子菜单...")
                            self._scan_event_log_submenus()
                            return True
                except:
                    continue
            
            print("❌ 未找到事件日志菜单")
            return False
            
        except Exception as e:
            print(f"❌ 导航到事件日志失败: {e}")
            return False
    
    def _scan_event_log_submenus(self):
        """扫描事件日志子菜单"""
        try:
            print("🔍 扫描事件日志子菜单...")
            
            # 查找所有可能的子菜单
            submenu_selectors = [
                "//ul[contains(@class, 'el-menu')]//span",
                "//*[contains(@class, 'submenu')]//span",
                "//*[contains(@class, 'menu-item')]//span"
            ]
            
            found_submenus = []
            
            for selector in submenu_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            text = elem.text.strip()
                            # 查找包含"日志"的子菜单
                            if '日志' in text and text not in [s['text'] for s in found_submenus]:
                                found_submenus.append({
                                    'text': text,
                                    'element': elem
                                })
                except:
                    continue
            
            print(f"   找到 {len(found_submenus)} 个日志子菜单:")
            for submenu in found_submenus:
                print(f"      - {submenu['text']}")
            
            # 扫描每个子菜单
            for submenu in found_submenus:
                try:
                    print(f"\n🔍 扫描子菜单: {submenu['text']}")
                    
                    # 点击子菜单
                    self.driver.execute_script("arguments[0].click();", submenu['element'])
                    time.sleep(3)
                    
                    # 分析页面
                    page_data = self.analyze_page_elements(submenu['text'])
                    if page_data:
                        safe_key = submenu['text'].replace(' ', '_').replace('/', '_')
                        self.scan_results[safe_key] = page_data
                    
                except Exception as e:
                    print(f"   扫描子菜单失败: {e}")
                    continue
            
        except Exception as e:
            print(f"扫描事件日志子菜单失败: {e}")
    
    def analyze_page_elements(self, page_name):
        """分析页面元素"""
        try:
            print(f"   分析页面: {page_name}")
            
            page_data = {
                'page_name': page_name,
                'page_url': self.driver.current_url,
                'scan_time': datetime.now().isoformat(),
                'selectors': {}
            }
            
            # 分析按钮
            buttons = self._get_buttons()
            if buttons:
                page_data['selectors']['buttons'] = buttons
                print(f"      找到 {len(buttons)} 个按钮")
            
            # 分析输入框
            inputs = self._get_inputs()
            if inputs:
                page_data['selectors']['inputs'] = inputs
                print(f"      找到 {len(inputs)} 个输入框")
            
            # 分析表格
            tables = self._get_tables()
            if tables:
                page_data['selectors']['tables'] = tables
                print(f"      找到 {len(tables)} 个表格")
            
            # 分析特殊元素（如详情按钮）
            special_elements = self._get_special_elements()
            if special_elements:
                page_data['selectors']['special_elements'] = special_elements
                print(f"      找到 {len(special_elements)} 个特殊元素")
            
            return page_data
            
        except Exception as e:
            print(f"   分析页面失败: {e}")
            return None
    
    def _get_buttons(self):
        """获取按钮选择器"""
        buttons = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//button")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    text = elem.text.strip()
                    if text and len(text) < 30:
                        buttons[text] = {
                            'xpath': f"//button[contains(text(), '{text}')]",
                            'class': elem.get_attribute('class')
                        }
        except:
            pass
        return buttons
    
    def _get_inputs(self):
        """获取输入框选择器"""
        inputs = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//input")
            for elem in elements:
                if elem.is_displayed():
                    name = elem.get_attribute('name')
                    placeholder = elem.get_attribute('placeholder')
                    input_type = elem.get_attribute('type')
                    
                    key = name or placeholder or f"input_{input_type}"
                    if key and key not in inputs:
                        inputs[key] = {
                            'xpath': f"//input[@name='{name}']" if name else f"//input[@placeholder='{placeholder}']" if placeholder else f"//input[@type='{input_type}']",
                            'type': input_type,
                            'name': name,
                            'placeholder': placeholder
                        }
        except:
            pass
        return inputs
    
    def _get_tables(self):
        """获取表格选择器"""
        tables = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//table[contains(@class, 'el-table')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    tables[f"table_{i+1}"] = {
                        'xpath': f"(//table[contains(@class, 'el-table')])[{i+1}]",
                        'class': elem.get_attribute('class')
                    }
        except:
            pass
        return tables
    
    def _get_special_elements(self):
        """获取特殊元素（如详情按钮、操作按钮等）"""
        special_elements = {}
        try:
            # 查找详情按钮
            detail_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), '详情')] | //a[contains(text(), '详情')]")
            if detail_buttons:
                special_elements['details_buttons'] = {
                    'xpath': "//button[contains(text(), '详情')] | //a[contains(text(), '详情')]",
                    'count': len([btn for btn in detail_buttons if btn.is_displayed()]),
                    'description': "详情按钮"
                }
            
            # 查找操作列按钮
            action_buttons = self.driver.find_elements(By.XPATH, "//td[contains(@class, 'action') or contains(., '操作')]//button")
            if action_buttons:
                special_elements['action_buttons'] = {
                    'xpath': "//td[contains(@class, 'action') or contains(., '操作')]//button",
                    'count': len([btn for btn in action_buttons if btn.is_displayed()]),
                    'description': "操作列按钮"
                }
            
            # 查找分页元素
            pagination = self.driver.find_elements(By.XPATH, "//*[contains(@class, 'el-pagination')]")
            if pagination:
                special_elements['pagination'] = {
                    'xpath': "//*[contains(@class, 'el-pagination')]",
                    'description': "分页组件"
                }
            
        except:
            pass
        return special_elements
    
    def update_existing_rules(self):
        """更新现有规则库"""
        try:
            print("\n📝 更新现有规则库...")
            
            # 读取现有规则
            json_file = os.path.join(self.output_dir, "ar_ui_selectors.json")
            existing_rules = {}
            
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    existing_rules = json.load(f)
                print(f"   读取现有规则: {len(existing_rules)} 个页面")
            
            # 移除错误的事件日志条目
            if "事件日志" in existing_rules:
                del existing_rules["事件日志"]
                print("   移除错误的事件日志条目")
            
            # 添加新的正确规则
            existing_rules.update(self.scan_results)
            
            # 保存更新后的规则
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(existing_rules, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 规则库更新完成: {len(existing_rules)} 个页面")
            return True
            
        except Exception as e:
            print(f"❌ 更新规则失败: {e}")
            return False
    
    def run_scan(self):
        """运行扫描"""
        print("🔍 事件日志修复扫描器")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login():
                return False
            
            if not self.navigate_to_event_logs():
                return False
            
            if not self.update_existing_rules():
                return False
            
            print("\n🎉 事件日志扫描修复完成！")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    scanner = EventLogsScanner()
    success = scanner.run_scan()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
