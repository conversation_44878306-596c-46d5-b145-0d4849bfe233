{"main_page": {"page_name": "主页面", "page_url": "http://172.16.103.202:8088/ui/#/dashboard", "scan_time": "2025-08-05T13:18:24.329081", "elements": {"buttons": [{"text": "最近7天", "class": "el-button el-button--default el-button--mini active", "type": "button", "disabled": false, "xpath_by_text": "//button[contains(text(), '最近7天')]", "xpath_by_class": "//button[contains(@class, 'el-button')]"}, {"text": "最近30天", "class": "el-button el-button--default el-button--mini", "type": "button", "disabled": false, "xpath_by_text": "//button[contains(text(), '最近30天')]", "xpath_by_class": "//button[contains(@class, 'el-button')]"}], "inputs": [], "tables": [], "checkboxes": [], "radios": [], "selects": [], "dialogs": [], "forms": []}}, "勒索病毒事件": {"page_name": "勒索病毒事件", "page_url": "http://172.16.103.202:8088/ui/#/logs/logs-malice-behavior", "scan_time": "2025-08-05T13:18:31.090992", "elements": {"buttons": [{"text": "刷新", "class": "el-button filter-item el-button--primary el-button--mini", "type": "button", "disabled": false, "xpath_by_text": "//button[contains(text(), '刷新')]", "xpath_by_class": "//button[contains(@class, 'el-button')]"}, {"text": "搜索", "class": "el-button filter-item el-button--primary el-button--mini", "type": "button", "disabled": false, "xpath_by_text": "//button[contains(text(), '搜索')]", "xpath_by_class": "//button[contains(@class, 'el-button')]"}, {"text": "", "class": "btn-prev", "type": "button", "disabled": true, "xpath_by_text": null, "xpath_by_class": "//button[contains(@class, 'btn-prev')]"}, {"text": "", "class": "btn-next", "type": "button", "disabled": true, "xpath_by_text": null, "xpath_by_class": "//button[contains(@class, 'btn-next')]"}], "inputs": [{"type": "text", "name": "", "id": "", "placeholder": "开始日期", "class": "el-range-input", "required": false, "xpath_by_name": null, "xpath_by_id": null, "xpath_by_placeholder": "//input[@placeholder='开始日期']"}, {"type": "text", "name": "", "id": "", "placeholder": "结束日期", "class": "el-range-input", "required": false, "xpath_by_name": null, "xpath_by_id": null, "xpath_by_placeholder": "//input[@placeholder='结束日期']"}, {"type": "text", "name": "", "id": "", "placeholder": "终端别名/IP", "class": "el-input__inner", "required": false, "xpath_by_name": null, "xpath_by_id": null, "xpath_by_placeholder": "//input[@placeholder='终端别名/IP']"}, {"type": "text", "name": "", "id": "", "placeholder": "请选择", "class": "el-input__inner", "required": false, "xpath_by_name": null, "xpath_by_id": null, "xpath_by_placeholder": "//input[@placeholder='请选择']"}, {"type": "number", "name": "", "id": "", "placeholder": "", "class": "el-input__inner", "required": false, "xpath_by_name": null, "xpath_by_id": null, "xpath_by_placeholder": null}], "tables": [{"index": 1, "class": "el-table__header", "headers": ["终端别名", "终端分组", "终端IP", "行为类型", "病毒名称", "病毒路径", "病毒执行参数", "处理结果", "病毒hash"], "xpath": "(//table)[1]", "xpath_by_class": "//table[contains(@class, 'el-table__header')]"}, {"index": 2, "class": "el-table__body", "headers": [], "xpath": "(//table)[2]", "xpath_by_class": "//table[contains(@class, 'el-table__body')]"}, {"index": 3, "class": "el-table__header", "headers": ["上报时间"], "xpath": "(//table)[3]", "xpath_by_class": "//table[contains(@class, 'el-table__header')]"}, {"index": 4, "class": "el-table__body", "headers": [], "xpath": "(//table)[4]", "xpath_by_class": "//table[contains(@class, 'el-table__body')]"}, {"index": 5, "class": "el-table__header", "headers": ["更多详情"], "xpath": "(//table)[5]", "xpath_by_class": "//table[contains(@class, 'el-table__header')]"}, {"index": 6, "class": "el-table__body", "headers": [], "xpath": "(//table)[6]", "xpath_by_class": "//table[contains(@class, 'el-table__body')]"}], "checkboxes": [], "radios": [], "selects": [{"type": "element-ui", "index": 1, "class": "el-select el-select--mini", "xpath": "(//div[contains(@class, 'el-select')])[1]"}], "dialogs": [], "forms": []}}, "备份与恢复": {"page_name": "备份与恢复", "page_url": "http://172.16.103.202:8088/ui/#/backup/backup", "scan_time": "2025-08-05T13:18:34.270781", "elements": {"buttons": [], "inputs": [], "tables": [], "checkboxes": [], "radios": [], "selects": [], "dialogs": [], "forms": []}}}