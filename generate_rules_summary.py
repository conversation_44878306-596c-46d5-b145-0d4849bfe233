#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成AR控制中心规则库总结报告
"""

import json
import os
from datetime import datetime

def generate_rules_summary():
    """生成规则库总结"""
    
    # 读取规则库
    rules_file = "ar_ui_rules/ar_ui_selectors.json"
    if not os.path.exists(rules_file):
        print("❌ 规则库文件不存在")
        return
    
    with open(rules_file, 'r', encoding='utf-8') as f:
        rules = json.load(f)
    
    print("📋 AR控制中心UI规则库总结报告")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总页面数: {len(rules)}")
    print()
    
    # 统计信息
    total_buttons = 0
    total_inputs = 0
    total_tables = 0
    total_checkboxes = 0
    total_special = 0
    
    # 按类别分组页面
    categories = {
        "主要功能页面": [],
        "事件日志页面": [],
        "管理页面": [],
        "其他页面": []
    }
    
    # 分析每个页面
    for page_key, page_data in rules.items():
        page_name = page_data.get('page_name', page_key)
        page_url = page_data.get('page_url', '')
        selectors = page_data.get('selectors', {})
        
        # 统计元素数量
        buttons = selectors.get('buttons', {})
        inputs = selectors.get('inputs', {})
        tables = selectors.get('tables', {})
        checkboxes = selectors.get('checkboxes', {})
        special = selectors.get('special_elements', {})
        
        total_buttons += len(buttons)
        total_inputs += len(inputs)
        total_tables += len(tables)
        total_checkboxes += len(checkboxes)
        total_special += len(special)
        
        # 分类页面
        if '日志' in page_name:
            categories["事件日志页面"].append({
                'name': page_name,
                'url': page_url,
                'elements': {
                    'buttons': len(buttons),
                    'inputs': len(inputs),
                    'tables': len(tables),
                    'checkboxes': len(checkboxes),
                    'special': len(special)
                }
            })
        elif page_name in ['用户管理', '控制中心升级', '客户端下载', '勒索病毒事件', '备份与恢复']:
            categories["管理页面"].append({
                'name': page_name,
                'url': page_url,
                'elements': {
                    'buttons': len(buttons),
                    'inputs': len(inputs),
                    'tables': len(tables),
                    'checkboxes': len(checkboxes),
                    'special': len(special)
                }
            })
        elif page_name in ['首页', '终端管理', '终端列表', '扫描任务', '升级任务', '计划任务', '防护策略', '分组策略', '白名单']:
            categories["主要功能页面"].append({
                'name': page_name,
                'url': page_url,
                'elements': {
                    'buttons': len(buttons),
                    'inputs': len(inputs),
                    'tables': len(tables),
                    'checkboxes': len(checkboxes),
                    'special': len(special)
                }
            })
        else:
            categories["其他页面"].append({
                'name': page_name,
                'url': page_url,
                'elements': {
                    'buttons': len(buttons),
                    'inputs': len(inputs),
                    'tables': len(tables),
                    'checkboxes': len(checkboxes),
                    'special': len(special)
                }
            })
    
    # 打印统计信息
    print("📊 元素统计:")
    print(f"   按钮: {total_buttons} 个")
    print(f"   输入框: {total_inputs} 个")
    print(f"   表格: {total_tables} 个")
    print(f"   复选框: {total_checkboxes} 个")
    print(f"   特殊元素: {total_special} 个")
    print(f"   总计: {total_buttons + total_inputs + total_tables + total_checkboxes + total_special} 个UI元素")
    print()
    
    # 打印页面分类
    for category, pages in categories.items():
        if pages:
            print(f"📁 {category} ({len(pages)}个):")
            for page in pages:
                elements_count = sum(page['elements'].values())
                print(f"   ✅ {page['name']} - {elements_count}个元素")
                if elements_count > 0:
                    details = []
                    if page['elements']['buttons'] > 0:
                        details.append(f"{page['elements']['buttons']}按钮")
                    if page['elements']['inputs'] > 0:
                        details.append(f"{page['elements']['inputs']}输入框")
                    if page['elements']['tables'] > 0:
                        details.append(f"{page['elements']['tables']}表格")
                    if page['elements']['checkboxes'] > 0:
                        details.append(f"{page['elements']['checkboxes']}复选框")
                    if page['elements']['special'] > 0:
                        details.append(f"{page['elements']['special']}特殊元素")
                    print(f"      ({', '.join(details)})")
            print()
    
    # 生成使用示例
    print("💡 使用示例:")
    print("```python")
    print("import json")
    print()
    print("# 加载规则库")
    print("with open('ar_ui_rules/ar_ui_selectors.json', 'r', encoding='utf-8') as f:")
    print("    rules = json.load(f)")
    print()
    print("# 获取终端列表页面的病毒扫描按钮")
    print("virus_scan_btn = rules['终端列表']['selectors']['buttons']['病毒扫描']['xpath']")
    print("# 结果: //button[contains(text(), '病毒扫描')]")
    print()
    print("# 获取终端复选框选择器")
    print("terminal_checkboxes = rules['终端列表']['selectors']['checkboxes']['terminal_checkboxes']['xpath']")
    print("# 结果: //span[contains(@class, 'el-checkbox__inner')]")
    print()
    print("# 获取扫描任务页面的详情按钮")
    print("details_btn = rules['扫描任务']['selectors']['special_elements']['details_buttons']['xpath']")
    print("# 结果: //button[contains(text(), '详情')] | //a[contains(text(), '详情')]")
    print("```")
    print()
    
    # 检查缺失的重要元素
    print("⚠️  需要注意的问题:")
    
    missing_elements = []
    
    # 检查终端列表页面是否有足够的元素
    if '终端列表' in rules:
        terminal_page = rules['终端列表']['selectors']
        if 'buttons' not in terminal_page or '病毒扫描' not in terminal_page.get('buttons', {}):
            missing_elements.append("终端列表页面缺少病毒扫描按钮")
    
    # 检查扫描任务页面是否有详情按钮
    if '扫描任务' in rules:
        scan_page = rules['扫描任务']['selectors']
        if 'special_elements' not in scan_page or 'details_buttons' not in scan_page.get('special_elements', {}):
            missing_elements.append("扫描任务页面缺少详情按钮选择器")
    
    if missing_elements:
        for issue in missing_elements:
            print(f"   ❌ {issue}")
    else:
        print("   ✅ 所有重要元素都已包含")
    
    print()
    print("🎉 规则库总结完成！")
    print("   规则库文件: ar_ui_rules/ar_ui_selectors.json")
    print("   Python选择器: ar_ui_rules/ar_selectors.py")

if __name__ == "__main__":
    generate_rules_summary()
