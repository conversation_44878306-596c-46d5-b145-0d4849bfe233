#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AR控制中心缺失页面扫描器
扫描之前没有找到的页面
"""

import time
import json
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class ARMissingPagesScanner:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.scan_results = {}
        
        self.config = {
            'url': 'http://172.16.103.202:8088',
            'username': 'admin',
            'password': 'Admin.2022',
            'timeout': 10
        }
        
        # 缺失的页面和它们可能的菜单路径
        self.missing_pages = {
            "用户管理": ["中心管理", "用户管理"],
            "系统配置": ["中心管理", "系统配置"],
            "控制中心升级": ["中心管理", "控制中心升级"],
            "客户端下载": ["客户端下载"],
            "勒索病毒事件": ["勒索病毒事件"],
            "备份与恢复": ["备份与恢复"]
        }
        
        # 输出目录
        self.output_dir = "ar_ui_rules"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def setup_driver(self):
        """设置浏览器"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.maximize_window()
            self.wait = WebDriverWait(self.driver, self.config['timeout'])
            
            print("✅ 浏览器设置成功")
            return True
        except Exception as e:
            print(f"❌ 浏览器设置失败: {e}")
            return False
    
    def login(self):
        """登录控制中心"""
        try:
            print("🔄 登录控制中心...")
            
            self.driver.get(self.config['url'])
            time.sleep(3)
            
            username_input = self.wait.until(EC.element_to_be_clickable((By.NAME, "username")))
            username_input.clear()
            username_input.send_keys(self.config['username'])
            
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(self.config['password'])
            
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            time.sleep(5)
            
            print("✅ 登录成功")
            return True
            
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    def navigate_to_page(self, page_name, menu_path):
        """根据菜单路径导航到页面"""
        try:
            print(f"🔄 导航到页面: {page_name} (路径: {' -> '.join(menu_path)})")
            
            # 按照菜单路径逐级点击
            for i, menu_item in enumerate(menu_path):
                print(f"   点击菜单 {i+1}: {menu_item}")
                
                # 查找菜单项
                menu_selectors = [
                    f"//span[contains(text(), '{menu_item}')]",
                    f"//a[contains(text(), '{menu_item}')]",
                    f"//*[text()='{menu_item}']",
                    f"//li[contains(., '{menu_item}')]//span",
                    f"//div[contains(@class, 'el-menu-item') and contains(., '{menu_item}')]"
                ]
                
                found = False
                for selector in menu_selectors:
                    try:
                        elements = self.driver.find_elements(By.XPATH, selector)
                        for elem in elements:
                            if elem.is_displayed() and menu_item in elem.text:
                                self.driver.execute_script("arguments[0].click();", elem)
                                time.sleep(2)
                                print(f"      ✅ 成功点击: {menu_item}")
                                found = True
                                break
                        if found:
                            break
                    except Exception as e:
                        print(f"      尝试选择器失败: {e}")
                        continue
                
                if not found:
                    print(f"      ❌ 未找到菜单项: {menu_item}")
                    return False
            
            print(f"✅ 成功导航到: {page_name}")
            return True
            
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
    
    def analyze_page_elements(self, page_name):
        """分析页面元素"""
        try:
            print(f"🔍 分析页面: {page_name}")
            
            page_data = {
                'page_name': page_name,
                'page_url': self.driver.current_url,
                'scan_time': datetime.now().isoformat(),
                'selectors': {}
            }
            
            # 分析按钮
            buttons = self._get_buttons()
            if buttons:
                page_data['selectors']['buttons'] = buttons
                print(f"   找到 {len(buttons)} 个按钮")
            
            # 分析输入框
            inputs = self._get_inputs()
            if inputs:
                page_data['selectors']['inputs'] = inputs
                print(f"   找到 {len(inputs)} 个输入框")
            
            # 分析表格
            tables = self._get_tables()
            if tables:
                page_data['selectors']['tables'] = tables
                print(f"   找到 {len(tables)} 个表格")
            
            # 分析复选框
            checkboxes = self._get_checkboxes()
            if checkboxes:
                page_data['selectors']['checkboxes'] = checkboxes
                print(f"   找到 {len(checkboxes)} 个复选框")
            
            # 分析单选框
            radios = self._get_radios()
            if radios:
                page_data['selectors']['radios'] = radios
                print(f"   找到 {len(radios)} 个单选框")
            
            return page_data
            
        except Exception as e:
            print(f"❌ 分析页面失败: {e}")
            return None
    
    def _get_buttons(self):
        """获取按钮选择器"""
        buttons = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//button")
            for elem in elements:
                if elem.is_displayed() and elem.text.strip():
                    text = elem.text.strip()
                    if text and len(text) < 30:  # 过滤掉太长的文本
                        buttons[text] = {
                            'xpath': f"//button[contains(text(), '{text}')]",
                            'class': elem.get_attribute('class')
                        }
        except:
            pass
        return buttons
    
    def _get_inputs(self):
        """获取输入框选择器"""
        inputs = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//input")
            for elem in elements:
                if elem.is_displayed():
                    name = elem.get_attribute('name')
                    placeholder = elem.get_attribute('placeholder')
                    input_type = elem.get_attribute('type')
                    
                    key = name or placeholder or f"input_{input_type}"
                    if key and key not in inputs:
                        inputs[key] = {
                            'xpath': f"//input[@name='{name}']" if name else f"//input[@placeholder='{placeholder}']" if placeholder else f"//input[@type='{input_type}']",
                            'type': input_type,
                            'name': name,
                            'placeholder': placeholder
                        }
        except:
            pass
        return inputs
    
    def _get_tables(self):
        """获取表格选择器"""
        tables = {}
        try:
            elements = self.driver.find_elements(By.XPATH, "//table[contains(@class, 'el-table')]")
            for i, elem in enumerate(elements):
                if elem.is_displayed():
                    tables[f"table_{i+1}"] = {
                        'xpath': f"(//table[contains(@class, 'el-table')])[{i+1}]",
                        'class': elem.get_attribute('class')
                    }
        except:
            pass
        return tables
    
    def _get_checkboxes(self):
        """获取复选框选择器"""
        checkboxes = {}
        try:
            # Element UI 复选框
            elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'el-checkbox__inner')]")
            if elements:
                checkboxes['el_checkboxes'] = {
                    'xpath': "//span[contains(@class, 'el-checkbox__inner')]",
                    'count': len([e for e in elements if e.is_displayed()]),
                    'description': "Element UI复选框"
                }
            
            # 原生复选框
            elements = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            if elements:
                checkboxes['native_checkboxes'] = {
                    'xpath': "//input[@type='checkbox']",
                    'count': len([e for e in elements if e.is_displayed()]),
                    'description': "原生复选框"
                }
        except:
            pass
        return checkboxes
    
    def _get_radios(self):
        """获取单选框选择器"""
        radios = {}
        try:
            # Element UI 单选框
            elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'el-radio__inner')]")
            if elements:
                radios['el_radios'] = {
                    'xpath': "//span[contains(@class, 'el-radio__inner')]",
                    'count': len([e for e in elements if e.is_displayed()]),
                    'description': "Element UI单选框"
                }
            
            # 原生单选框
            elements = self.driver.find_elements(By.XPATH, "//input[@type='radio']")
            if elements:
                radios['native_radios'] = {
                    'xpath': "//input[@type='radio']",
                    'count': len([e for e in elements if e.is_displayed()]),
                    'description': "原生单选框"
                }
        except:
            pass
        return radios
    
    def scan_missing_pages(self):
        """扫描缺失的页面"""
        try:
            print(f"\n🚀 开始扫描 {len(self.missing_pages)} 个缺失页面...")
            
            for i, (page_name, menu_path) in enumerate(self.missing_pages.items()):
                print(f"\n{'='*20} 扫描页面: {page_name} ({i+1}/{len(self.missing_pages)}) {'='*20}")
                
                if self.navigate_to_page(page_name, menu_path):
                    page_data = self.analyze_page_elements(page_name)
                    if page_data:
                        safe_key = page_name.replace(' ', '_').replace('/', '_')
                        self.scan_results[safe_key] = page_data
                else:
                    print(f"   ⚠️ 跳过页面: {page_name}")
            
            print(f"\n✅ 扫描完成！共扫描了 {len(self.scan_results)} 个页面")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
    
    def merge_with_existing_rules(self):
        """合并到现有规则库"""
        try:
            print("\n📝 合并到现有规则库...")
            
            # 读取现有规则
            json_file = os.path.join(self.output_dir, "ar_ui_selectors.json")
            existing_rules = {}
            
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    existing_rules = json.load(f)
                print(f"   读取现有规则: {len(existing_rules)} 个页面")
            
            # 合并新规则
            existing_rules.update(self.scan_results)
            
            # 保存合并后的规则
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(existing_rules, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 规则库更新完成: {len(existing_rules)} 个页面")
            return True
            
        except Exception as e:
            print(f"❌ 合并规则失败: {e}")
            return False
    
    def run_scan(self):
        """运行扫描"""
        print("🔍 AR控制中心缺失页面扫描器")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            if not self.login():
                return False
            
            if not self.scan_missing_pages():
                return False
            
            if not self.merge_with_existing_rules():
                return False
            
            print("\n🎉 缺失页面扫描完成！")
            return True
            
        except Exception as e:
            print(f"❌ 扫描失败: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()

def main():
    scanner = ARMissingPagesScanner()
    success = scanner.run_scan()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
