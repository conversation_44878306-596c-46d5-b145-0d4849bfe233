{
  "首页": {
    "page_name": "首页",
    "page_url": "http://**************:8088/ui/#/dashboard",
    "scan_time": "2025-08-05T13:11:01.996008",
    "selectors": {
      "buttons": {
        "最近7天": {
          "xpath": "//button[contains(text(), '最近7天')]",
          "class": "el-button el-button--default el-button--mini active"
        },
        "最近30天": {
          "xpath": "//button[contains(text(), '最近30天')]",
          "class": "el-button el-button--default el-button--mini"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "终端管理": {
    "page_name": "终端管理",
    "page_url": "http://**************:8088/ui/#/dashboard",
    "scan_time": "2025-08-05T13:11:05.179027",
    "selectors": {
      "buttons": {
        "最近7天": {
          "xpath": "//button[contains(text(), '最近7天')]",
          "class": "el-button el-button--default el-button--mini active"
        },
        "最近30天": {
          "xpath": "//button[contains(text(), '最近30天')]",
          "class": "el-button el-button--default el-button--mini"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "终端列表": {
    "page_name": "终端列表",
    "page_url": "http://**************:8088/ui/#/client/list",
    "scan_time": "2025-08-05T13:11:08.323812",
    "selectors": {
      "buttons": {
        "病毒扫描": {
          "xpath": "//button[contains(text(), '病毒扫描')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "终端升级": {
          "xpath": "//button[contains(text(), '终端升级')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "移动分组": {
          "xpath": "//button[contains(text(), '移动分组')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--default el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "终端别名/IP/操作系统": {
          "xpath": "//input[@placeholder='终端别名/IP/操作系统']",
          "type": "text",
          "name": "",
          "placeholder": "终端别名/IP/操作系统"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        },
        "main_table_3": {
          "xpath": "(//table[contains(@class, 'el-table')])[3]",
          "class": "el-table__header"
        },
        "main_table_4": {
          "xpath": "(//table[contains(@class, 'el-table')])[4]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "扫描任务": {
    "page_name": "扫描任务",
    "page_url": "http://**************:8088/ui/#/client/scan",
    "scan_time": "2025-08-05T13:11:12.061750",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "详情": {
          "xpath": "//button[contains(text(), '详情')]",
          "class": "el-button el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "任务序号": {
          "xpath": "//input[@placeholder='任务序号']",
          "type": "text",
          "name": "",
          "placeholder": "任务序号"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"http://**************:8088/ui/#/
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "升级任务": {
    "page_name": "升级任务",
    "page_url": "http://**************:8088/ui/#/client/update-task",
    "scan_time": "2025-08-05T13:11:15.375410",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "任务序号": {
          "xpath": "//input[@placeholder='任务序号']",
          "type": "text",
          "name": "",
          "placeholder": "任务序号"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "计划任务": {
    "page_name": "计划任务",
    "page_url": "http://**************:8088/ui/#/client/sched-task",
    "scan_time": "2025-08-05T13:11:18.582927",
    "selectors": {
      "buttons": {
        "创建": {
          "xpath": "//button[contains(text(), '创建')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "任务名称": {
          "xpath": "//input[@placeholder='任务名称']",
          "type": "text",
          "name": "",
          "placeholder": "任务名称"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "防护策略": {
    "page_name": "防护策略",
    "page_url": "http://**************:8088/ui/#/client/sched-task",
    "scan_time": "2025-08-05T13:11:21.790565",
    "selectors": {
      "buttons": {
        "创建": {
          "xpath": "//button[contains(text(), '创建')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "任务名称": {
          "xpath": "//input[@placeholder='任务名称']",
          "type": "text",
          "name": "",
          "placeholder": "任务名称"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "分组策略": {
    "page_name": "分组策略",
    "page_url": "http://**************:8088/ui/#/protectstrategy/group-strategy",
    "scan_time": "2025-08-05T13:11:25.000831",
    "selectors": {
      "buttons": {
        "新增策略": {
          "xpath": "//button[contains(text(), '新增策略')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "重新分配": {
          "xpath": "//button[contains(text(), '重新分配')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "取消": {
          "xpath": "//button[contains(text(), '取消')]",
          "class": "el-button el-button--default el-button--medium is-disabled"
        },
        "保存": {
          "xpath": "//button[contains(text(), '保存')]",
          "class": "el-button el-button--primary el-button--medium is-disabled"
        }
      },
      "inputs": {
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "白名单": {
    "page_name": "白名单",
    "page_url": "http://**************:8088/ui/#/protectstrategy/whitelist",
    "scan_time": "2025-08-05T13:11:28.188847",
    "selectors": {
      "buttons": {
        "新增": {
          "xpath": "//button[contains(text(), '新增')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "修改": {
          "xpath": "//button[contains(text(), '修改')]",
          "class": "el-button el-button--primary el-button--mini"
        },
        "删除": {
          "xpath": "//button[contains(text(), '删除')]",
          "class": "el-button el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "白名单/备注": {
          "xpath": "//input[@placeholder='白名单/备注']",
          "type": "text",
          "name": "",
          "placeholder": "白名单/备注"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "事件日志": {
    "page_name": "事件日志",
    "page_url": "http://**************:8088/ui/#/protectstrategy/whitelist",
    "scan_time": "2025-08-05T13:11:31.438129",
    "selectors": {
      "buttons": {
        "新增": {
          "xpath": "//button[contains(text(), '新增')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "修改": {
          "xpath": "//button[contains(text(), '修改')]",
          "class": "el-button el-button--primary el-button--mini"
        },
        "删除": {
          "xpath": "//button[contains(text(), '删除')]",
          "class": "el-button el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "白名单/备注": {
          "xpath": "//input[@placeholder='白名单/备注']",
          "type": "text",
          "name": "",
          "placeholder": "白名单/备注"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "病毒日志": {
    "page_name": "病毒日志",
    "page_url": "http://**************:8088/ui/#/logs/log-scan",
    "scan_time": "2025-08-05T13:11:34.715687",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "开始日期": {
          "xpath": "//input[@placeholder='开始日期']",
          "type": "text",
          "name": "",
          "placeholder": "开始日期"
        },
        "结束日期": {
          "xpath": "//input[@placeholder='结束日期']",
          "type": "text",
          "name": "",
          "placeholder": "结束日期"
        },
        "任务序号/终端别名/病毒名称/IP": {
          "xpath": "//input[@placeholder='任务序号/终端别名/病毒名称/IP']",
          "type": "text",
          "name": "",
          "placeholder": "任务序号/终端别名/病毒名称/IP"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "文件实时防护日志": {
    "page_name": "文件实时防护日志",
    "page_url": "http://**************:8088/ui/#/logs/logs-file-monitor",
    "scan_time": "2025-08-05T13:11:37.931182",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "开始日期": {
          "xpath": "//input[@placeholder='开始日期']",
          "type": "text",
          "name": "",
          "placeholder": "开始日期"
        },
        "结束日期": {
          "xpath": "//input[@placeholder='结束日期']",
          "type": "text",
          "name": "",
          "placeholder": "结束日期"
        },
        "终端别名/病毒名称/IP": {
          "xpath": "//input[@placeholder='终端别名/病毒名称/IP']",
          "type": "text",
          "name": "",
          "placeholder": "终端别名/病毒名称/IP"
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_3": {
          "xpath": "(//table[contains(@class, 'el-table')])[3]",
          "class": "el-table__header"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "终端运行日志": {
    "page_name": "终端运行日志",
    "page_url": "http://**************:8088/ui/#/logs/log-operation-client",
    "scan_time": "2025-08-05T13:11:41.174821",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "开始日期": {
          "xpath": "//input[@placeholder='开始日期']",
          "type": "text",
          "name": "",
          "placeholder": "开始日期"
        },
        "结束日期": {
          "xpath": "//input[@placeholder='结束日期']",
          "type": "text",
          "name": "",
          "placeholder": "结束日期"
        },
        "请选择功能模块": {
          "xpath": "//input[@placeholder='请选择功能模块']",
          "type": "text",
          "name": "",
          "placeholder": "请选择功能模块"
        },
        "终端别名/IP": {
          "xpath": "//input[@placeholder='终端别名/IP']",
          "type": "text",
          "name": "",
          "placeholder": "终端别名/IP"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  },
  "中心操作日志": {
    "page_name": "中心操作日志",
    "page_url": "http://**************:8088/ui/#/logs/log-operation-center",
    "scan_time": "2025-08-05T13:11:44.451580",
    "selectors": {
      "buttons": {
        "刷新": {
          "xpath": "//button[contains(text(), '刷新')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        },
        "搜索": {
          "xpath": "//button[contains(text(), '搜索')]",
          "class": "el-button filter-item el-button--primary el-button--mini"
        }
      },
      "inputs": {
        "开始日期": {
          "xpath": "//input[@placeholder='开始日期']",
          "type": "text",
          "name": "",
          "placeholder": "开始日期"
        },
        "结束日期": {
          "xpath": "//input[@placeholder='结束日期']",
          "type": "text",
          "name": "",
          "placeholder": "结束日期"
        },
        "请选择功能模块": {
          "xpath": "//input[@placeholder='请选择功能模块']",
          "type": "text",
          "name": "",
          "placeholder": "请选择功能模块"
        },
        "请选择": {
          "xpath": "//input[@placeholder='请选择']",
          "type": "text",
          "name": "",
          "placeholder": "请选择"
        },
        "input_number": {
          "xpath": "//input[@type='number']",
          "type": "number",
          "name": "",
          "placeholder": ""
        }
      },
      "tables": {
        "main_table_1": {
          "xpath": "(//table[contains(@class, 'el-table')])[1]",
          "class": "el-table__header"
        },
        "main_table_2": {
          "xpath": "(//table[contains(@class, 'el-table')])[2]",
          "class": "el-table__body"
        }
      },
      "checkboxes": {
        "terminal_checkboxes": {
          "xpath": "//span[contains(@class, 'el-checkbox__inner')]",
          "description": "终端列表复选框",
          "usage": "用于选择终端，需要结合行内容判断是否为终端复选框"
        }
      }
    }
  }
}